% === Enhanced Parallel HEV Simulink Model with EMS and Speed Input ===
modelName = 'Enhanced_Parallel_HEV_Model';

% Check if model already exists and close it
if bdIsLoaded(modelName)
    close_system(modelName, 0);
end

new_system(modelName);
open_system(modelName);

% === Add Battery Model (Simplified) ===
add_block('simulink/Sources/Constant', [modelName '/Battery Voltage'], ...
    'Position', [50, 50, 150, 80], 'Value', '400');

% === Add Motor Model (Simplified) ===
add_block('simulink/Math Operations/Gain', [modelName '/Motor'], ...
    'Position', [200, 30, 250, 60], 'Gain', '0.9');

% === Add Engine Model (Simplified) ===
add_block('simulink/Math Operations/Gain', [modelName '/Engine'], ...
    'Position', [200, 150, 250, 180], 'Gain', '0.8');

% === Add Clutch Model (Switch) ===
add_block('simulink/Signal Routing/Switch', [modelName '/Clutch'], ...
    'Position', [350, 90, 380, 120]);

% === Add Vehicle Dynamics (Integrator) ===
add_block('simulink/Continuous/Integrator', [modelName '/Vehicle Speed'], ...
    'Position', [450, 90, 480, 120]);

% === Add Speed Sensor (Gain block for simplicity) ===
add_block('simulink/Math Operations/Gain', [modelName '/Speed Sensor'], ...
    'Position', [500, 90, 530, 120], 'Gain', '1');

% === Add Scope ===
add_block('simulink/Sinks/Scope', [modelName '/Scope'], ...
    'Position', [600, 90, 650, 120]);

% === Add EMS Controller (Simplified as Gain) ===
add_block('simulink/Math Operations/Gain', [modelName '/EMS Controller'], ...
    'Position', [300, 200, 350, 230], 'Gain', '1');

% === Add Speed Input (Constant for simplicity) ===
add_block('simulink/Sources/Constant', [modelName '/Speed Command'], ...
    'Position', [50, 200, 150, 230], 'Value', '60');

% === Add Sum Block for Torque Addition ===
add_block('simulink/Math Operations/Sum', [modelName '/Torque Sum'], ...
    'Position', [300, 90, 330, 120], 'Inputs', '++');

% === Connect all blocks ===
try
    % Connect battery voltage to motor and engine
    add_line(modelName, 'Battery Voltage/1', 'Motor/1');
    add_line(modelName, 'Battery Voltage/1', 'Engine/1');

    % Connect motor and engine outputs to sum block
    add_line(modelName, 'Motor/1', 'Torque Sum/1');
    add_line(modelName, 'Engine/1', 'Torque Sum/2');

    % Connect sum output to clutch
    add_line(modelName, 'Torque Sum/1', 'Clutch/1');

    % Connect clutch to vehicle speed integrator
    add_line(modelName, 'Clutch/1', 'Vehicle Speed/1');

    % Connect vehicle speed to speed sensor
    add_line(modelName, 'Vehicle Speed/1', 'Speed Sensor/1');

    % Connect speed sensor to scope
    add_line(modelName, 'Speed Sensor/1', 'Scope/1');

    % Connect speed command to EMS controller
    add_line(modelName, 'Speed Command/1', 'EMS Controller/1');

    % Add clutch control signal
    add_block('simulink/Sources/Constant', [modelName '/Clutch Control'], ...
        'Position', [250, 50, 300, 70], 'Value', '1');
    add_line(modelName, 'Clutch Control/1', 'Clutch/2');

catch ME
    warning('Connection error: %s', ME.message);
end

% === Save the model ===
save_system(modelName);
disp(['Enhanced model "', modelName, '" created and saved successfully.']);
