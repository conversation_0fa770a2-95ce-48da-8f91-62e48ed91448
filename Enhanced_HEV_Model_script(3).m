% === Enhanced Parallel HEV Simulink Model with EMS and Speed Input ===
modelName = 'Enhanced_Parallel_HEV_Model';
new_system(modelName);
open_system(modelName);

% === Add Solver Configuration ===
add_block('simscape/Utilities/Solver Configuration', [modelName '/Solver'], ...
    'Position', [50, 50, 100, 100]);

% === Add Battery ===
add_block('simscape/Electrical/Specialized Power Systems/Sources/Battery', ...
    [modelName '/Battery'], 'Position', [150, 50, 250, 100]);

% === Add Motor ===
add_block('simscape/Electrical/Specialized Power Systems/Machines/Universal Machine', ...
    [modelName '/Motor'], 'Position', [350, 30, 500, 120]);

% === Add Engine ===
add_block('simscape/Driveline/Engines/SI Engine', [modelName '/Engine'], ...
    'Position', [350, 150, 500, 250]);

% === Add Clutch ===
add_block('simscape/Driveline/Couplings/Clutch', [modelName '/Clutch'], ...
    'Position', [600, 90, 650, 200]);

% === Add Vehicle Body ===
add_block('simscape/Driveline/Vehicle Components/Vehicle Body', ...
    [modelName '/VehicleBody'], 'Position', [750, 90, 850, 200]);

% === Add Speed Sensor ===
add_block('simscape/Driveline/Sensors/Rotational Motion Sensor', ...
    [modelName '/SpeedSensor'], 'Position', [850, 50, 900, 100]);

% === Add PS to Simulink Converter ===
add_block('simscape/Utilities/PS-Simulink Converter', [modelName '/PS2Simulink'], ...
    'Position', [900, 70, 940, 110]);

% === Add Scope ===
add_block('simulink/Sinks/Scope', [modelName '/Scope'], ...
    'Position', [1000, 70, 1050, 120]);

% === Add EMS Controller Subsystem ===
add_block('simulink/Ports & Subsystems/Subsystem', [modelName '/EMS Controller'], ...
    'Position', [500, 300, 650, 380]);

% === Add Speed Input (From Workspace) ===
add_block('simulink/Sources/From Workspace', [modelName '/Speed Command'], ...
    'Position', [100, 300, 200, 340], ...
    'VariableName', 'speed_command');

% === Add Simulink-PS Converter ===
add_block('simscape/Utilities/Simulink-PS Converter', [modelName '/Sim2PS'], ...
    'Position', [250, 300, 300, 340]);

% === Connect all blocks ===
add_line(modelName, 'Battery/RConn1', 'Motor/RConn1');
add_line(modelName, 'Motor/RConn2', 'Clutch/L');
add_line(modelName, 'Engine/RConn1', 'Clutch/R');
add_line(modelName, 'Clutch/F', 'VehicleBody/R');
add_line(modelName, 'VehicleBody/W', 'SpeedSensor/R');
add_line(modelName, 'SpeedSensor/S', 'PS2Simulink/1');
add_line(modelName, 'PS2Simulink/1', 'Scope/1');
add_line(modelName, 'Solver/RConn1', 'Battery/RConn2');
add_line(modelName, 'Speed Command/1', 'Sim2PS/1');
add_line(modelName, 'Sim2PS/1', 'EMS Controller/1');

% === Save the model ===
save_system(modelName);
disp(['Enhanced model "', modelName, '" created and saved successfully.']);
