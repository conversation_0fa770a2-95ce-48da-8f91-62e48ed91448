% === Auto Transmission Model ===
model = 'AutoTransmissionModel_v2';

% Check if model already exists and close it
if bdIsLoaded(model)
    close_system(model, 0);
end

new_system(model);
open_system(model);

% === Positions ===
x = 30; y = 30; w = 50; h = 30; dx = 100; dy = 80;

% 1. Drive Torque
add_block('simulink/Commonly Used Blocks/Constant', [model '/Drive Torque'], ...
    'Position', [x y x+w y+h], 'Value', '1');

% 2. f(x)=0
add_block('simulink/Math Operations/Gain', [model '/f(x)=0'], ...
    'Gain', '0', 'Position', [x+dx y x+dx+w y+h]);

% 3. Product
add_block('simulink/Math Operations/Product', [model '/Product'], ...
    'Position', [x+2*dx y x+2*dx+w y+h]);

% 4. Scope
add_block('simulink/Sinks/Scope', [model '/Scope'], ...
    'Position', [x+3*dx y x+3*dx+w y+h]);

% 5. Gear High
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Gear High'], ...
    'Position', [x+4*dx y x+4*dx+70 y+h]);

% 6. Clutch Schedule
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Clutch Schedule'], ...
    'Position', [x+2*dx y+dy x+2*dx+100 y+dy+h]);

% 7. Clutch Pressures
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Clutch Pressures'], ...
    'Position', [x+3*dx+50 y+dy x+3*dx+150 y+dy+h]);

% 8. Gear Low
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Gear Low'], ...
    'Position', [x+4*dx y+dy x+4*dx+70 y+dy+h]);

% 9. Clutch Brake
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Clutch Brake'], ...
    'Position', [x+5*dx+10 y+dy/2 x+5*dx+100 y+dy/2+h]);

% 10. Brake Pressure
add_block('simulink/Commonly Used Blocks/Constant', [model '/Brake Pressure'], ...
    'Value', '1', 'Position', [x+dx y+2*dy x+dx+w y+2*dy+h]);

% 11. Gear Pressure
add_block('simulink/Commonly Used Blocks/Constant', [model '/Gear Pressure'], ...
    'Value', '1', 'Position', [x+dx y+2*dy+50 x+dx+w y+2*dy+50+h]);

% === Connections ===
add_line(model, 'Drive Torque/1', 'f(x)=0/1');
add_line(model, 'f(x)=0/1', 'Product/1');
add_line(model, 'Product/1', 'Scope/1');
add_line(model, 'Scope/1', 'Gear High/1');

add_line(model, 'Clutch Schedule/1', 'Clutch Pressures/1');
add_line(model, 'Clutch Pressures/1', 'Gear Low/1');

% === Save & View ===
save_system(model);
disp(['Auto Transmission Model "', model, '" created successfully.']);



% === Create Parallel HEV Model ===
modelName = 'Parallel_HEV_Model';

% Check if model already exists and close it
if bdIsLoaded(modelName)
    close_system(modelName, 0);
end

new_system(modelName);
open_system(modelName);

% === Add Solver Configuration ===
add_block('simscape/Utilities/Solver Configuration', [modelName '/Solver'], ...
    'Position', [50, 50, 100, 100]);

% === Add Battery Block ===
add_block('simscape/Electrical/Specialized Power Systems/Sources/Battery', ...
    [modelName '/Battery'], ...
    'Position', [150, 50, 250, 100]);

% === Add Motor (DC Machine) ===
add_block('simscape/Electrical/Specialized Power Systems/Machines/Universal Machine', ...
    [modelName '/Motor'], ...
    'Position', [350, 30, 500, 120]);

% === Add SI Engine ===
add_block('simscape/Driveline/Engines/SI Engine', ...
    [modelName '/Engine'], ...
    'Position', [350, 150, 500, 250]);

% === Add Clutch ===
add_block('simscape/Driveline/Couplings/Clutch', ...
    [modelName '/Clutch'], ...
    'Position', [600, 90, 650, 200]);

% === Add Vehicle Body ===
add_block('simscape/Driveline/Vehicle Components/Vehicle Body', ...
    [modelName '/VehicleBody'], ...
    'Position', [750, 90, 850, 200]);

% === Add Scope ===
add_block('simulink/Sinks/Scope', ...
    [modelName '/Scope'], ...
    'Position', [950, 100, 1000, 150]);

% === Add Speed Sensor ===
add_block('simscape/Driveline/Sensors/Rotational Motion Sensor', ...
    [modelName '/SpeedSensor'], ...
    'Position', [850, 50, 900, 100]);

% === Add PS-Simulink Converter ===
add_block('simscape/Utilities/PS-Simulink Converter', ...
    [modelName '/PS2Simulink'], ...
    'Position', [900, 70, 940, 110]);

% === Connect Blocks ===
try
    add_line(modelName, 'Battery/RConn1', 'Motor/RConn1');
    add_line(modelName, 'Motor/RConn2', 'Clutch/L');
    add_line(modelName, 'Engine/RConn1', 'Clutch/R');
    add_line(modelName, 'Clutch/F', 'VehicleBody/R');
    add_line(modelName, 'VehicleBody/W', 'SpeedSensor/R');
    add_line(modelName, 'SpeedSensor/S', 'PS2Simulink/1');
    add_line(modelName, 'PS2Simulink/1', 'Scope/1');
    add_line(modelName, 'Solver/RConn1', 'Battery/RConn2');
catch ME
    warning('Connection error: %s', ME.message);
end

% === Save Model ===
save_system(modelName);

disp(['Parallel HEV Model "', modelName, '" has been built successfully.']);
