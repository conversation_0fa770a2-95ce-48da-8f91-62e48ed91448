% === Auto Transmission Model ===
model = 'AutoTransmissionModel_v2';

% Check if model already exists and close it
if bdIsLoaded(model)
    close_system(model, 0);
end

new_system(model);
open_system(model);

% === Positions ===
x = 30; y = 30; w = 50; h = 30; dx = 100; dy = 80;

% 1. Drive Torque
add_block('simulink/Commonly Used Blocks/Constant', [model '/Drive Torque'], ...
    'Position', [x y x+w y+h], 'Value', '1');

% 2. f(x)=0
add_block('simulink/Math Operations/Gain', [model '/f(x)=0'], ...
    'Gain', '0', 'Position', [x+dx y x+dx+w y+h]);

% 3. Product
add_block('simulink/Math Operations/Product', [model '/Product'], ...
    'Position', [x+2*dx y x+2*dx+w y+h]);

% 4. Scope
add_block('simulink/Sinks/Scope', [model '/Scope'], ...
    'Position', [x+3*dx y x+3*dx+w y+h]);

% 5. Gear High
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Gear High'], ...
    'Position', [x+4*dx y x+4*dx+70 y+h]);

% 6. Clutch Schedule
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Clutch Schedule'], ...
    'Position', [x+2*dx y+dy x+2*dx+100 y+dy+h]);

% 7. Clutch Pressures
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Clutch Pressures'], ...
    'Position', [x+3*dx+50 y+dy x+3*dx+150 y+dy+h]);

% 8. Gear Low
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Gear Low'], ...
    'Position', [x+4*dx y+dy x+4*dx+70 y+dy+h]);

% 9. Clutch Brake
add_block('simulink/Ports & Subsystems/Subsystem', [model '/Clutch Brake'], ...
    'Position', [x+5*dx+10 y+dy/2 x+5*dx+100 y+dy/2+h]);

% 10. Brake Pressure
add_block('simulink/Commonly Used Blocks/Constant', [model '/Brake Pressure'], ...
    'Value', '1', 'Position', [x+dx y+2*dy x+dx+w y+2*dy+h]);

% 11. Gear Pressure
add_block('simulink/Commonly Used Blocks/Constant', [model '/Gear Pressure'], ...
    'Value', '1', 'Position', [x+dx y+2*dy+50 x+dx+w y+2*dy+50+h]);

% === Connections ===
add_line(model, 'Drive Torque/1', 'f(x)=0/1');
add_line(model, 'f(x)=0/1', 'Product/1');
add_line(model, 'Product/1', 'Scope/1');

add_line(model, 'Clutch Schedule/1', 'Clutch Pressures/1');
add_line(model, 'Clutch Pressures/1', 'Gear Low/1');

% === Save & View ===
save_system(model);
disp(['Auto Transmission Model "', model, '" created successfully.']);


% === Create Parallel HEV Model ===
modelName = 'Parallel_HEV_Model';

% Check if model already exists and close it
if bdIsLoaded(modelName)
    close_system(modelName, 0);
end

new_system(modelName);
open_system(modelName);

% === Add Input Signal (Torque Command) ===
add_block('simulink/Sources/Constant', [modelName '/Torque Command'], ...
    'Position', [50, 50, 100, 80], 'Value', '100');

% === Add Battery Model (Simplified) ===
add_block('simulink/Sources/Constant', [modelName '/Battery Voltage'], ...
    'Position', [150, 50, 250, 80], 'Value', '400');

% === Add Motor Model (Simplified) ===
add_block('simulink/Math Operations/Gain', [modelName '/Motor'], ...
    'Position', [350, 30, 400, 60], 'Gain', '0.9');

% === Add Engine Model (Simplified) ===
add_block('simulink/Math Operations/Gain', [modelName '/Engine'], ...
    'Position', [350, 150, 400, 180], 'Gain', '0.8');

% === Add Clutch Model (Switch) ===
add_block('simulink/Signal Routing/Switch', [modelName '/Clutch'], ...
    'Position', [500, 90, 530, 120]);

% === Add Vehicle Dynamics (Integrator) ===
add_block('simulink/Continuous/Integrator', [modelName '/Vehicle Speed'], ...
    'Position', [600, 90, 630, 120]);

% === Add Scope ===
add_block('simulink/Sinks/Scope', [modelName '/Scope'], ...
    'Position', [700, 90, 750, 120]);

% === Add Sum Block for Torque Addition ===
add_block('simulink/Math Operations/Sum', [modelName '/Torque Sum'], ...
    'Position', [450, 90, 480, 120], 'Inputs', '++');

% === Connect Blocks ===
try
    % Connect torque command to motor and engine
    add_line(modelName, 'Torque Command/1', 'Motor/1');
    add_line(modelName, 'Torque Command/1', 'Engine/1');

    % Connect motor and engine outputs to sum block
    add_line(modelName, 'Motor/1', 'Torque Sum/1');
    add_line(modelName, 'Engine/1', 'Torque Sum/2');

    % Connect sum output to clutch
    add_line(modelName, 'Torque Sum/1', 'Clutch/1');

    % Connect clutch to vehicle speed integrator
    add_line(modelName, 'Clutch/1', 'Vehicle Speed/1');

    % Connect vehicle speed to scope
    add_line(modelName, 'Vehicle Speed/1', 'Scope/1');

    % Add clutch control signal (constant for simplicity)
    add_block('simulink/Sources/Constant', [modelName '/Clutch Control'], ...
        'Position', [450, 50, 480, 70], 'Value', '1');
    add_line(modelName, 'Clutch Control/1', 'Clutch/2');

catch ME
    warning('Connection error: %s', ME.message);
end

% === Save Model ===
save_system(modelName);

disp(['Parallel HEV Model "', modelName, '" has been built successfully.']);
