% === Test Script for Created Models ===

% Test Auto Transmission Model
try
    disp('Testing Auto Transmission Model...');
    model1 = 'AutoTransmissionModel_v2';
    
    if bdIsLoaded(model1)
        % Set simulation parameters
        set_param(model1, 'StopTime', '10');
        set_param(model1, 'Solver', 'ode45');
        
        % Run simulation
        simOut1 = sim(model1);
        disp(['✓ Auto Transmission Model simulation completed successfully']);
    else
        disp('⚠ Auto Transmission Model not loaded. Running script first...');
        En_model_script_3;
    end
catch ME
    disp(['✗ Error in Auto Transmission Model: ', ME.message]);
end

fprintf('\n');

% Test Parallel HEV Model
try
    disp('Testing Parallel HEV Model...');
    model2 = 'Parallel_HEV_Model';
    
    if bdIsLoaded(model2)
        % Set simulation parameters
        set_param(model2, 'StopTime', '10');
        set_param(model2, 'Solver', 'ode45');
        
        % Run simulation
        simOut2 = sim(model2);
        disp(['✓ Parallel HEV Model simulation completed successfully']);
    else
        disp('⚠ Parallel HEV Model not loaded. Running script first...');
        En_model_script_3;
    end
catch ME
    disp(['✗ Error in Parallel HEV Model: ', ME.message]);
end

fprintf('\n');

% Test Enhanced Parallel HEV Model
try
    disp('Testing Enhanced Parallel HEV Model...');
    model3 = 'Enhanced_Parallel_HEV_Model';
    
    if bdIsLoaded(model3)
        % Set simulation parameters
        set_param(model3, 'StopTime', '10');
        set_param(model3, 'Solver', 'ode45');
        
        % Run simulation
        simOut3 = sim(model3);
        disp(['✓ Enhanced Parallel HEV Model simulation completed successfully']);
    else
        disp('⚠ Enhanced Parallel HEV Model not loaded. Running script first...');
        Enhanced_HEV_Model_script_3;
    end
catch ME
    disp(['✗ Error in Enhanced Parallel HEV Model: ', ME.message]);
end

fprintf('\n');
disp('=== Model Testing Complete ===');

% Display model information
try
    models = find_system('Type', 'block_diagram');
    disp('Currently loaded models:');
    for i = 1:length(models)
        if ~strcmp(models{i}, 'simulink')
            disp(['  - ', models{i}]);
        end
    end
catch
    disp('Could not retrieve model list');
end
